"use client"

import { useState, useMemo } from "react"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"

interface CalendarEvent {
  id: string
  title: string
  startTime: string // HH:MM format
  endTime: string // HH:MM format
  date: string // YYYY-MM-DD format
  color: "purple" | "orange" | "green" | "blue" | "yellow" | "red"
  isAllDay?: boolean
}

const sampleEvents: CalendarEvent[] = [
  {
    id: "1",
    title: "Jojo Birthday Party",
    startTime: "00:00",
    endTime: "23:59",
    date: "2024-01-04",
    color: "yellow",
    isAllDay: true,
  },
]

const timeSlots = Array.from({ length: 24 }, (_, i) => {
  const hour = i.toString().padStart(2, "0")
  return `${hour}:00`
})

const colorClasses = {
  purple:
    "bg-purple-200 border-purple-300 text-purple-800 dark:bg-purple-900/50 dark:border-purple-700 dark:text-purple-200",
  orange:
    "bg-orange-200 border-orange-300 text-orange-800 dark:bg-orange-900/50 dark:border-orange-700 dark:text-orange-200",
  green: "bg-green-200 border-green-300 text-green-800 dark:bg-green-900/50 dark:border-green-700 dark:text-green-200",
  blue: "bg-blue-200 border-blue-300 text-blue-800 dark:bg-blue-900/50 dark:border-blue-700 dark:text-blue-200",
  yellow:
    "bg-yellow-200 border-yellow-300 text-yellow-800 dark:bg-yellow-900/50 dark:border-yellow-700 dark:text-yellow-200",
  red: "bg-red-200 border-red-300 text-red-800 dark:bg-red-900/50 dark:border-red-700 dark:text-red-200",
}

export function WeeklyCalendar() {
  const [currentWeek, setCurrentWeek] = useState(new Date("2024-01-01"))
  const [currentTime] = useState("08:3")

  const weekDays = useMemo(() => {
    const startOfWeek = new Date(currentWeek)
    const day = startOfWeek.getDay()
    const diff = startOfWeek.getDate() - day
    startOfWeek.setDate(diff)

    return Array.from({ length: 7 }, (_, i) => {
      const date = new Date(startOfWeek)
      date.setDate(startOfWeek.getDate() + i)
      return date
    })
  }, [currentWeek])

  const formatDate = (date: Date) => {
    return date.toISOString().split("T")[0]
  }

  const formatDayHeader = (date: Date) => {
    const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
    return `${dayNames[date.getDay()]} ${date.getDate()}`
  }

  const timeToMinutes = (time: string) => {
    const [hours, minutes] = time.split(":").map(Number)
    return hours * 60 + minutes
  }

  const getEventPosition = (event: CalendarEvent) => {
    const startMinutes = timeToMinutes(event.startTime)
    const endMinutes = timeToMinutes(event.endTime)
    const duration = endMinutes - startMinutes

    // Each hour is 60px, so each minute is 1px
    const top = (startMinutes / 60) * 60
    const height = Math.max((duration / 60) * 60, 20) // Minimum height of 20px

    return { top, height }
  }

  const getEventsForDay = (date: Date) => {
    const dateStr = formatDate(date)
    return sampleEvents.filter((event) => event.date === dateStr && !event.isAllDay)
  }

  const getAllDayEventsForDay = (date: Date) => {
    const dateStr = formatDate(date)
    return sampleEvents.filter((event) => event.date === dateStr && event.isAllDay)
  }

  const getCurrentTimePosition = () => {
    const minutes = timeToMinutes(currentTime)
    return (minutes / 60) * 60
  }

  const navigateWeek = (direction: "prev" | "next") => {
    const newWeek = new Date(currentWeek)
    newWeek.setDate(currentWeek.getDate() + (direction === "next" ? 7 : -7))
    setCurrentWeek(newWeek)
  }

  const isToday = (date: Date) => {
    const today = new Date("2024-01-03") // Simulating today as Wed 3
    return formatDate(date) === formatDate(today)
  }

  return (
    <Card className="w-full overflow-hidden">
      {/* Header with navigation */}
      <div className="flex items-center justify-between border-b p-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => navigateWeek("prev")}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={() => navigateWeek("next")}>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <h2 className="text-lg font-semibold">
            {weekDays[0].toLocaleDateString("en-US", { month: "long", year: "numeric" })}
          </h2>
        </div>
      </div>

      <div className="overflow-x-auto">
        <div className="min-w-[800px]">
          {/* Day headers */}
          <div className="grid grid-cols-8 border-b">
            <div className="p-2 text-sm font-medium text-muted-foreground">{/* Empty cell for time column */}</div>
            {weekDays.map((day, index) => (
              <div
                key={index}
                className={cn(
                  "p-2 text-center text-sm font-medium border-l",
                  isToday(day) && "bg-red-50 dark:bg-red-950/20",
                )}
              >
                <div
                  className={cn(
                    "inline-flex items-center justify-center rounded-full w-8 h-8",
                    isToday(day) && "bg-red-500 text-white",
                  )}
                >
                  {formatDayHeader(day)}
                </div>
              </div>
            ))}
          </div>

          {/* All-day events row */}
          <div className="grid grid-cols-8 border-b bg-muted/30">
            <div className="p-2 text-xs text-muted-foreground font-medium">all-day</div>
            {weekDays.map((day, dayIndex) => (
              <div key={dayIndex} className="border-l p-1 min-h-[40px] relative">
                {getAllDayEventsForDay(day).map((event, eventIndex) => (
                  <div
                    key={event.id}
                    className={cn("text-xs p-1 rounded border mb-1 truncate", colorClasses[event.color])}
                  >
                    {event.title}
                  </div>
                ))}
              </div>
            ))}
          </div>

          {/* Time grid */}
          <div className="relative">
            {/* Current time indicator */}
            <div
              className="absolute left-0 right-0 z-20 flex items-center"
              style={{ top: `${getCurrentTimePosition()}px` }}
            >
              <div className="bg-red-500 text-white text-xs px-1 rounded text-nowrap">{currentTime}</div>
              <div className="flex-1 h-0.5 bg-red-500"></div>
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            </div>

            <div className="grid grid-cols-8">
              {/* Time column */}
              <div className="relative">
                {timeSlots.map((time, index) => (
                  <div key={time} className="h-[60px] border-b text-xs text-muted-foreground p-2 flex items-start">
                    {time}
                  </div>
                ))}
              </div>

              {/* Day columns */}
              {weekDays.map((day, dayIndex) => (
                <div key={dayIndex} className="relative border-l">
                  {/* Hour grid lines */}
                  {timeSlots.map((time, timeIndex) => (
                    <div key={time} className="h-[60px] border-b border-border/50" />
                  ))}

                  {/* Events */}
                  <div className="absolute inset-0 p-1">
                    {getEventsForDay(day).map((event, eventIndex) => {
                      const { top, height } = getEventPosition(event)
                      return (
                        <div
                          key={event.id}
                          className={cn(
                            "absolute left-1 right-1 p-1 rounded border text-xs overflow-hidden z-10",
                            colorClasses[event.color],
                          )}
                          style={{
                            top: `${top}px`,
                            height: `${height}px`,
                          }}
                        >
                          <div className="font-medium truncate">{event.title}</div>
                          <div className="text-xs opacity-75">
                            {event.startTime}–{event.endTime}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}